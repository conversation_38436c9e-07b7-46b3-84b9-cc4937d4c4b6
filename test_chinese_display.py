import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from matplotlib.font_manager import FontProperties
import os

def test_chinese_display():
    """测试中文字体显示效果"""
    print("🧪 测试中文字体显示效果...")
    
    # 设置字体
    font_path = 'C:/Windows/Fonts/msyh.ttc'
    if os.path.exists(font_path):
        chinese_font = FontProperties(fname=font_path)
        print(f"✅ 使用字体文件: {font_path}")
    else:
        chinese_font = None
        print("⚠️ 使用系统字体配置")
    
    # 配置matplotlib
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'SimSun']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建测试图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # 测试1: 基本中文文字
    ax1.text(0.5, 0.5, '中文字体测试\n数据可视化\n北京二手房分析', 
             ha='center', va='center', fontsize=16, fontweight='bold')
    ax1.set_title('基本中文文字测试', fontsize=14, fontweight='bold')
    ax1.axis('off')
    
    # 测试2: 图表标签
    categories = ['朝阳区', '通州区', '丰台区', '昌平区', '顺义区']
    values = [78000, 65000, 45000, 38000, 32000]
    
    bars = ax2.bar(categories, values, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'])
    ax2.set_title('各区域房价对比', fontsize=14, fontweight='bold')
    ax2.set_xlabel('所在区', fontsize=12, fontweight='bold')
    ax2.set_ylabel('平均单价（元/平方米）', fontsize=12, fontweight='bold')
    ax2.tick_params(axis='x', rotation=45)
    
    # 添加数值标签
    for bar in bars:
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 1000,
                f'{int(height):,}', ha='center', va='bottom', fontweight='bold')
    
    # 测试3: 图例
    import numpy as np
    x = np.linspace(0, 10, 100)
    ax3.plot(x, np.sin(x), label='第一产业', linewidth=2, color='#FF6B6B')
    ax3.plot(x, np.cos(x), label='第二产业', linewidth=2, color='#4ECDC4')
    ax3.plot(x, np.sin(x) * np.cos(x), label='第三产业', linewidth=2, color='#45B7D1')
    ax3.set_title('产业发展趋势', fontsize=14, fontweight='bold')
    ax3.set_xlabel('时间（季度）', fontsize=12, fontweight='bold')
    ax3.set_ylabel('增加值（亿元）', fontsize=12, fontweight='bold')
    ax3.legend(fontsize=10)
    ax3.grid(True, alpha=0.3)
    
    # 测试4: 饼图
    labels = ['会员顾客', '普通顾客']
    sizes = [62.3, 37.7]
    colors = ['#FF6B6B', '#4ECDC4']
    
    ax4.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', 
            startangle=90, textprops={'fontsize': 12, 'fontweight': 'bold'})
    ax4.set_title('顾客类型分布', fontsize=14, fontweight='bold')
    
    # 总标题
    fig.suptitle('中文字体显示测试报告', fontsize=18, fontweight='bold', y=0.95)
    
    plt.tight_layout()
    plt.savefig('chinese_font_test_result.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ 中文字体测试完成")
    print("📁 测试结果保存为: chinese_font_test_result.png")
    
    # 输出字体信息
    print(f"\n📋 字体配置信息:")
    print(f"  当前字体设置: {plt.rcParams['font.sans-serif']}")
    print(f"  Unicode减号: {plt.rcParams['axes.unicode_minus']}")
    
    # 检查系统中文字体
    all_fonts = [f.name for f in fm.fontManager.ttflist]
    chinese_fonts = [f for f in all_fonts if any(keyword in f for keyword in 
                    ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi'])]
    print(f"  系统中文字体: {list(set(chinese_fonts))}")

if __name__ == "__main__":
    test_chinese_display()
