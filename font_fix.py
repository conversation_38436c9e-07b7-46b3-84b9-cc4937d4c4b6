import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import numpy as np

def test_chinese_fonts():
    """测试中文字体显示"""
    print("正在测试中文字体...")
    
    # 获取系统所有字体
    all_fonts = [f.name for f in fm.fontManager.ttflist]
    
    # 查找中文字体
    chinese_fonts = []
    for font in all_fonts:
        if any(keyword in font for keyword in ['SimHei', 'SimSun', 'Microsoft', 'YaHei', 'KaiTi', 'FangSong']):
            chinese_fonts.append(font)
    
    chinese_fonts = list(set(chinese_fonts))
    print(f"找到的中文字体: {chinese_fonts}")
    
    # 测试每个字体
    test_text = "中文测试文字"
    
    for i, font in enumerate(chinese_fonts[:3]):  # 只测试前3个
        try:
            plt.figure(figsize=(8, 4))
            plt.rcParams['font.sans-serif'] = [font]
            plt.rcParams['axes.unicode_minus'] = False
            
            plt.text(0.5, 0.5, f"{test_text} - {font}", 
                    fontsize=16, ha='center', va='center')
            plt.title(f"字体测试: {font}")
            plt.axis('off')
            
            plt.savefig(f'font_test_{i}.png', dpi=150, bbox_inches='tight')
            plt.show()
            print(f"✅ {font} - 测试成功")
            
        except Exception as e:
            print(f"❌ {font} - 测试失败: {e}")
    
    return chinese_fonts

def setup_best_chinese_font():
    """设置最佳中文字体"""
    # 按优先级排序的中文字体列表
    font_priority = [
        'Microsoft YaHei',
        'SimHei', 
        'SimSun',
        'KaiTi',
        'FangSong'
    ]
    
    # 获取系统可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    
    # 选择最佳字体
    selected_font = None
    for font in font_priority:
        if font in available_fonts:
            selected_font = font
            break
    
    if selected_font is None:
        print("⚠️ 未找到中文字体，使用默认字体")
        selected_font = 'DejaVu Sans'
    else:
        print(f"✅ 选择字体: {selected_font}")
    
    # 设置matplotlib参数
    plt.rcParams['font.sans-serif'] = [selected_font]
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.size'] = 12
    
    # 清除字体缓存
    fm._rebuild()
    
    return selected_font

def create_test_chart():
    """创建测试图表"""
    # 设置字体
    font = setup_best_chinese_font()
    
    # 创建测试数据
    categories = ['北京', '上海', '广州', '深圳', '杭州']
    values = [100, 85, 75, 90, 80]
    
    # 创建图表
    plt.figure(figsize=(10, 6))
    bars = plt.bar(categories, values, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'])
    
    # 添加标题和标签
    plt.title('中文字体测试图表', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('城市', fontsize=14, fontweight='bold')
    plt.ylabel('数值', fontsize=14, fontweight='bold')
    
    # 添加数值标签
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{int(height)}', ha='center', va='bottom', fontweight='bold')
    
    plt.grid(True, alpha=0.3, axis='y')
    plt.tight_layout()
    
    # 保存图表
    plt.savefig('chinese_font_test.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ 测试图表已保存为 chinese_font_test.png，使用字体: {font}")

if __name__ == "__main__":
    print("=== 中文字体修复工具 ===")
    
    # 测试字体
    available_fonts = test_chinese_fonts()
    
    print("\n=== 创建测试图表 ===")
    # 创建测试图表
    create_test_chart()
    
    print("\n=== 修复建议 ===")
    if available_fonts:
        print("✅ 系统中有可用的中文字体")
        print("建议使用的字体设置代码:")
        print("""
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'SimSun']
plt.rcParams['axes.unicode_minus'] = False
import matplotlib.font_manager as fm
fm._rebuild()  # 重建字体缓存
        """)
    else:
        print("❌ 系统中没有找到中文字体")
        print("请安装中文字体或使用以下解决方案:")
        print("1. 下载并安装中文字体文件")
        print("2. 使用在线字体服务")
        print("3. 将图表文字改为英文")
