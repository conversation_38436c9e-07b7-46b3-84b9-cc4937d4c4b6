import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体 - 修复版本
import matplotlib.font_manager as fm

def setup_chinese_font():
    """设置中文字体支持"""
    # 按优先级排序的中文字体
    chinese_fonts = [
        'Microsoft YaHei',
        'SimHei',
        'SimSun',
        'KaiTi',
        'FangSong',
        'Arial Unicode MS',
        'DejaVu Sans'
    ]

    # 获取系统可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]

    # 选择第一个可用的字体
    selected_font = 'Arial'  # 最后的备选字体
    for font in chinese_fonts:
        if font in available_fonts:
            selected_font = font
            print(f"✅ 找到字体: {selected_font}")
            break

    if selected_font == 'Arial':
        print("⚠️ 未找到理想的中文字体，使用默认字体")

    # 设置matplotlib参数
    plt.rcParams['font.sans-serif'] = [selected_font, 'Arial', 'sans-serif']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.size'] = 12

    # 测试中文显示
    try:
        fig, ax = plt.subplots(figsize=(1, 1))
        ax.text(0.5, 0.5, '测试', fontsize=12)
        plt.close(fig)
        print(f"✅ 中文字体测试成功: {selected_font}")
    except Exception as e:
        print(f"⚠️ 字体测试警告: {e}")

    return selected_font

# 初始化中文字体
print("正在配置中文字体...")
selected_font = setup_chinese_font()

# 设置图表样式
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("husl")

# 创建输出目录
import os
if not os.path.exists('charts'):
    os.makedirs('charts')

print("数据可视化环境配置完成！")
print("可用中文字体: Microsoft YaHei, SimHei, SimSun")

# 加载数据集
def load_datasets():
    """加载所有数据集"""
    datasets = {}

    # 二手房数据
    datasets['house'] = pd.read_excel('数据可视化数据集-A/二手房数据.xlsx')

    # 餐厅消费记录
    datasets['restaurant'] = pd.read_excel('数据可视化数据集-A/某餐厅顾客消费记录.xlsx')

    # GDP数据
    datasets['gdp'] = pd.read_excel('数据可视化数据集-A/国内生产总值季度数据.xlsx')

    return datasets

# 数据预处理
def preprocess_data(datasets):
    """数据预处理"""

    # GDP数据转换
    gdp_df = datasets['gdp'].copy()
    gdp_melted = pd.melt(gdp_df, id_vars=['指标'], var_name='季度', value_name='数值')
    gdp_melted['年份'] = gdp_melted['季度'].str[:4]
    gdp_melted['季度_简'] = gdp_melted['季度'].str[5:]
    datasets['gdp_processed'] = gdp_melted

    return datasets

# 二手房数据可视化函数
def create_house_charts(house_df):
    """创建二手房数据的3个图表"""

    # 图表1: 散点图 - 面积与总价的关系
    plt.figure(figsize=(12, 8))
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']

    for i, district in enumerate(house_df['所在区'].unique()):
        district_data = house_df[house_df['所在区'] == district]
        plt.scatter(district_data['面积（平方米）'], district_data['总价（万元）'],
                   alpha=0.6, s=60, label=district, color=colors[i % len(colors)])

    plt.xlabel('面积（平方米）', fontsize=14, fontweight='bold')
    plt.ylabel('总价（万元）', fontsize=14, fontweight='bold')
    plt.title('北京二手房面积与总价关系分析', fontsize=16, fontweight='bold', pad=20)
    plt.legend(title='所在区', title_fontsize=12, fontsize=10)
    plt.grid(True, alpha=0.3)

    # 添加趋势线
    z = np.polyfit(house_df['面积（平方米）'], house_df['总价（万元）'], 1)
    p = np.poly1d(z)
    plt.plot(house_df['面积（平方米）'], p(house_df['面积（平方米）']),
             "r--", alpha=0.8, linewidth=2, label='趋势线')

    plt.tight_layout()
    plt.savefig('charts/house_scatter.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 图表2: 箱形图 - 各区域房价分布
    plt.figure(figsize=(14, 8))

    # 计算各区域数据量用于排序
    district_counts = house_df['所在区'].value_counts()
    sorted_districts = district_counts.index.tolist()

    box_plot = plt.boxplot([house_df[house_df['所在区'] == district]['单价（元/平方米）']
                           for district in sorted_districts],
                          labels=sorted_districts, patch_artist=True)

    # 设置箱形图颜色
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']
    for patch, color in zip(box_plot['boxes'], colors):
        patch.set_facecolor(color)
        patch.set_alpha(0.7)

    plt.xlabel('所在区', fontsize=14, fontweight='bold')
    plt.ylabel('单价（元/平方米）', fontsize=14, fontweight='bold')
    plt.title('北京各区二手房单价分布箱形图', fontsize=16, fontweight='bold', pad=20)
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3, axis='y')

    # 添加均值点
    means = [house_df[house_df['所在区'] == district]['单价（元/平方米）'].mean()
             for district in sorted_districts]
    plt.scatter(range(1, len(sorted_districts) + 1), means,
               color='red', s=100, marker='D', label='均值', zorder=5)
    plt.legend()

    plt.tight_layout()
    plt.savefig('charts/house_boxplot.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 图表3: 条形图 - 各区域平均单价对比
    plt.figure(figsize=(12, 8))

    avg_price = house_df.groupby('所在区')['单价（元/平方米）'].mean().sort_values(ascending=False)

    bars = plt.bar(avg_price.index, avg_price.values,
                   color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'],
                   alpha=0.8, edgecolor='white', linewidth=2)

    plt.xlabel('所在区', fontsize=14, fontweight='bold')
    plt.ylabel('平均单价（元/平方米）', fontsize=14, fontweight='bold')
    plt.title('北京各区二手房平均单价对比', fontsize=16, fontweight='bold', pad=20)

    # 添加数值标签
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 1000,
                f'{int(height):,}', ha='center', va='bottom', fontweight='bold')

    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3, axis='y')
    plt.tight_layout()
    plt.savefig('charts/house_barplot.png', dpi=300, bbox_inches='tight')
    plt.show()

# 餐厅消费数据可视化函数
def create_restaurant_charts(restaurant_df):
    """创建餐厅消费数据的3个图表"""

    # 图表4: 小提琴图 - 不同分店消费金额分布
    plt.figure(figsize=(12, 8))

    # 创建小提琴图
    parts = plt.violinplot([restaurant_df[restaurant_df['分店'] == store]['消费金额（元）']
                           for store in restaurant_df['分店'].unique()],
                          positions=range(1, len(restaurant_df['分店'].unique()) + 1),
                          showmeans=True, showmedians=True)

    # 设置颜色
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    for pc, color in zip(parts['bodies'], colors):
        pc.set_facecolor(color)
        pc.set_alpha(0.7)

    plt.xlabel('分店', fontsize=14, fontweight='bold')
    plt.ylabel('消费金额（元）', fontsize=14, fontweight='bold')
    plt.title('各分店顾客消费金额分布小提琴图', fontsize=16, fontweight='bold', pad=20)
    plt.xticks(range(1, len(restaurant_df['分店'].unique()) + 1),
               restaurant_df['分店'].unique())
    plt.grid(True, alpha=0.3, axis='y')

    plt.tight_layout()
    plt.savefig('charts/restaurant_violin.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 图表5: 气泡图 - 消费金额与满意度关系（按性别分组）
    plt.figure(figsize=(12, 8))

    # 按性别分组绘制气泡图
    for gender, color in zip(['男', '女'], ['#4ECDC4', '#FF6B6B']):
        gender_data = restaurant_df[restaurant_df['性别'] == gender]

        # 计算气泡大小（基于数据点密度）
        bubble_sizes = []
        for _, row in gender_data.iterrows():
            # 计算周围相似数据点的数量作为气泡大小
            similar_count = len(gender_data[
                (abs(gender_data['消费金额（元）'] - row['消费金额（元）']) < 50) &
                (abs(gender_data['顾客满意度'] - row['顾客满意度']) < 10)
            ])
            bubble_sizes.append(max(20, similar_count * 10))

        plt.scatter(gender_data['消费金额（元）'], gender_data['顾客满意度'],
                   s=bubble_sizes, alpha=0.6, color=color, label=gender, edgecolors='white')

    plt.xlabel('消费金额（元）', fontsize=14, fontweight='bold')
    plt.ylabel('顾客满意度', fontsize=14, fontweight='bold')
    plt.title('顾客消费金额与满意度关系气泡图', fontsize=16, fontweight='bold', pad=20)
    plt.legend(title='性别', title_fontsize=12, fontsize=10)
    plt.grid(True, alpha=0.3)

    # 添加趋势线
    z = np.polyfit(restaurant_df['消费金额（元）'], restaurant_df['顾客满意度'], 1)
    p = np.poly1d(z)
    plt.plot(restaurant_df['消费金额（元）'], p(restaurant_df['消费金额（元）']),
             "r--", alpha=0.8, linewidth=2, label='趋势线')

    plt.tight_layout()
    plt.savefig('charts/restaurant_bubble.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 图表6: 环形图 - 顾客类型占比
    plt.figure(figsize=(10, 8))

    customer_type_counts = restaurant_df['顾客类型'].value_counts()

    # 创建环形图
    colors = ['#FF6B6B', '#4ECDC4']
    _, _, autotexts = plt.pie(customer_type_counts.values,
                             labels=customer_type_counts.index,
                             colors=colors, autopct='%1.1f%%',
                             startangle=90, pctdistance=0.85,
                             textprops={'fontsize': 12, 'fontweight': 'bold'})

    # 创建中心空白区域形成环形
    centre_circle = plt.Circle((0,0), 0.70, fc='white')
    fig = plt.gcf()
    fig.gca().add_artist(centre_circle)

    plt.title('餐厅顾客类型分布环形图', fontsize=16, fontweight='bold', pad=20)

    # 在中心添加总数信息
    plt.text(0, 0, f'总顾客数\n{len(restaurant_df)}',
             ha='center', va='center', fontsize=14, fontweight='bold')

    plt.axis('equal')
    plt.tight_layout()
    plt.savefig('charts/restaurant_donut.png', dpi=300, bbox_inches='tight')
    plt.show()

# GDP数据可视化函数
def create_gdp_charts(gdp_processed_df):
    """创建GDP数据的3个图表"""

    # 图表7: 折线图 - GDP及各产业增长趋势
    plt.figure(figsize=(14, 8))

    # 准备数据
    gdp_trend = gdp_processed_df.pivot(index='季度', columns='指标', values='数值')

    # 绘制折线图
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
    for i, column in enumerate(gdp_trend.columns):
        plt.plot(gdp_trend.index, gdp_trend[column],
                marker='o', linewidth=3, markersize=8,
                color=colors[i], label=column, alpha=0.8)

    plt.xlabel('季度', fontsize=14, fontweight='bold')
    plt.ylabel('增加值（亿元）', fontsize=14, fontweight='bold')
    plt.title('中国GDP及各产业增长趋势图（2019-2022）', fontsize=16, fontweight='bold', pad=20)
    plt.legend(fontsize=10, loc='upper left')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)

    # 添加数据标注（仅显示部分关键点）
    for i in range(0, len(gdp_trend.index), 4):  # 每年显示一次
        for j, column in enumerate(gdp_trend.columns):
            if j == 0:  # 只为GDP总值添加标注
                plt.annotate(f'{gdp_trend.iloc[i, j]:.0f}',
                           (gdp_trend.index[i], gdp_trend.iloc[i, j]),
                           textcoords="offset points", xytext=(0,10), ha='center',
                           fontsize=9, fontweight='bold')

    plt.tight_layout()
    plt.savefig('charts/gdp_line.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 图表8: 面积图 - 三大产业占比变化
    plt.figure(figsize=(14, 8))

    # 计算各产业占比
    industry_data = gdp_trend[['第一产业增加值（亿元）', '第二产业增加值（亿元）', '第三产业增加值（亿元）']]
    industry_pct = industry_data.div(industry_data.sum(axis=1), axis=0) * 100

    # 创建面积图
    plt.stackplot(industry_pct.index,
                 industry_pct['第一产业增加值（亿元）'],
                 industry_pct['第二产业增加值（亿元）'],
                 industry_pct['第三产业增加值（亿元）'],
                 labels=['第一产业', '第二产业', '第三产业'],
                 colors=['#96CEB4', '#4ECDC4', '#FF6B6B'],
                 alpha=0.8)

    plt.xlabel('季度', fontsize=14, fontweight='bold')
    plt.ylabel('占比（%）', fontsize=14, fontweight='bold')
    plt.title('中国三大产业结构变化面积图（2019-2022）', fontsize=16, fontweight='bold', pad=20)
    plt.legend(loc='upper right', fontsize=10)
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3, axis='y')

    plt.tight_layout()
    plt.savefig('charts/gdp_area.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 图表9: 雷达图 - 各季度三大产业对比
    plt.figure(figsize=(12, 10))

    # 选择最近4个季度的数据
    recent_quarters = gdp_trend.tail(4)
    industries = ['第一产业增加值（亿元）', '第二产业增加值（亿元）', '第三产业增加值（亿元）']

    # 设置雷达图
    angles = np.linspace(0, 2 * np.pi, len(industries), endpoint=False).tolist()
    angles += angles[:1]  # 闭合图形

    _, ax = plt.subplots(figsize=(12, 10), subplot_kw=dict(projection='polar'))

    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']

    for i, (quarter, row) in enumerate(recent_quarters.iterrows()):
        values = [row[industry] for industry in industries]
        values += values[:1]  # 闭合图形

        ax.plot(angles, values, 'o-', linewidth=2,
               label=quarter, color=colors[i], alpha=0.8)
        ax.fill(angles, values, alpha=0.25, color=colors[i])

    # 设置标签
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(['第一产业', '第二产业', '第三产业'], fontsize=12, fontweight='bold')
    ax.set_title('各季度三大产业增加值雷达图', fontsize=16, fontweight='bold', pad=30)
    ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0), fontsize=10)
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('charts/gdp_radar.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    # 加载和预处理数据
    datasets = load_datasets()
    datasets = preprocess_data(datasets)

    print("数据加载完成！")
    for name, df in datasets.items():
        if name != 'gdp_processed':
            print(f"{name}: {df.shape}")

    # 创建所有图表
    print("\n正在创建二手房数据可视化图表...")
    create_house_charts(datasets['house'])

    print("\n正在创建餐厅消费数据可视化图表...")
    create_restaurant_charts(datasets['restaurant'])

    print("\n正在创建GDP数据可视化图表...")
    create_gdp_charts(datasets['gdp_processed'])

    print("\n所有图表创建完成！图表已保存到 charts/ 目录中。")
